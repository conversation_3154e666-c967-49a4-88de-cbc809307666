/**
 * Comprehensive Integration Test Suite
 * End-to-end integration tests for complete system workflows
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { createTestContext, mockEnvVars } from '../mocks/mock-factory.js';
import { testConfig, switchTestEnvironment } from '../utils/test-config.js';

const testContext = createTestContext();

describe('Comprehensive Integration Tests', () => {
  beforeEach(() => {
    testContext.cleanup();
    // Ensure we're in local test mode
    switchTestEnvironment('local');
  });

  describe('User Authentication Flow', () => {
    it('should complete full user registration and login flow', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'SecurePassword123!',
        name: 'Test User'
      };

      // Step 1: User Registration
      testContext.supabase.auth.signInWithPassword.mockResolvedValueOnce({
        data: { 
          user: { id: 'user-123', email: userData.email },
          session: { access_token: 'access-token-123' }
        },
        error: null
      });

      const registrationResult = await testContext.supabase.auth.signInWithPassword({
        email: userData.email,
        password: userData.password
      });

      expect(registrationResult.data.user).toBeDefined();
      expect(registrationResult.data.session).toBeDefined();
      expect(registrationResult.error).toBeNull();

      // Step 2: Session Creation
      const sessionId = 'session-123';
      testContext.redis.set.mockResolvedValueOnce('OK');
      testContext.redis.expire.mockResolvedValueOnce(1);

      await testContext.redis.set(`session:${sessionId}`, JSON.stringify({
        userId: registrationResult.data.user.id,
        email: registrationResult.data.user.email,
        loginTime: Date.now()
      }));

      await testContext.redis.expire(`session:${sessionId}`, 3600);

      // Step 3: Verify Session
      testContext.redis.get.mockResolvedValueOnce(JSON.stringify({
        userId: 'user-123',
        email: userData.email,
        loginTime: Date.now()
      }));

      const sessionData = await testContext.redis.get(`session:${sessionId}`);
      const parsedSession = JSON.parse(sessionData);

      expect(parsedSession.userId).toBe('user-123');
      expect(parsedSession.email).toBe(userData.email);
    });

    it('should handle authentication failures gracefully', async () => {
      const invalidCredentials = {
        email: '<EMAIL>',
        password: 'wrongpassword'
      };

      testContext.supabase.auth.signInWithPassword.mockResolvedValueOnce({
        data: { user: null, session: null },
        error: { message: 'Invalid credentials' }
      });

      const result = await testContext.supabase.auth.signInWithPassword(invalidCredentials);

      expect(result.data.user).toBeNull();
      expect(result.data.session).toBeNull();
      expect(result.error).toBeDefined();
      expect(result.error.message).toBe('Invalid credentials');
    });
  });

  describe('Data Management Workflow', () => {
    it('should complete full CRUD operations with caching', async () => {
      const testData = {
        id: 'item-123',
        name: 'Test Item',
        description: 'Test Description',
        category: 'test'
      };

      // Step 1: Create Data
      testContext.supabase.from.mockReturnValue({
        insert: vi.fn().mockReturnValue({
          select: vi.fn().mockResolvedValue({
            data: [testData],
            error: null
          })
        })
      });

      const createResult = await testContext.supabase
        .from('items')
        .insert(testData)
        .select();

      expect(createResult.data).toEqual([testData]);
      expect(createResult.error).toBeNull();

      // Step 2: Cache the Data
      const cacheKey = `item:${testData.id}`;
      testContext.redis.set.mockResolvedValueOnce('OK');
      testContext.redis.expire.mockResolvedValueOnce(1);

      await testContext.redis.set(cacheKey, JSON.stringify(testData));
      await testContext.redis.expire(cacheKey, 3600);

      // Step 3: Read from Cache
      testContext.redis.get.mockResolvedValueOnce(JSON.stringify(testData));

      const cachedData = await testContext.redis.get(cacheKey);
      const parsedData = JSON.parse(cachedData);

      expect(parsedData).toEqual(testData);

      // Step 4: Update Data
      const updatedData = { ...testData, name: 'Updated Test Item' };
      
      testContext.supabase.from.mockReturnValue({
        update: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            select: vi.fn().mockResolvedValue({
              data: [updatedData],
              error: null
            })
          })
        })
      });

      const updateResult = await testContext.supabase
        .from('items')
        .update({ name: 'Updated Test Item' })
        .eq('id', testData.id)
        .select();

      expect(updateResult.data).toEqual([updatedData]);

      // Step 5: Invalidate Cache
      testContext.redis.del.mockResolvedValueOnce(1);
      const deletedCount = await testContext.redis.del(cacheKey);
      expect(deletedCount).toBe(1);

      // Step 6: Delete Data
      testContext.supabase.from.mockReturnValue({
        delete: vi.fn().mockReturnValue({
          eq: vi.fn().mockResolvedValue({
            data: null,
            error: null
          })
        })
      });

      const deleteResult = await testContext.supabase
        .from('items')
        .delete()
        .eq('id', testData.id);

      expect(deleteResult.error).toBeNull();
    });
  });

  describe('Real-time Communication Workflow', () => {
    it('should handle WebSocket connections and messaging', async () => {
      const mockWebSocket = {
        send: vi.fn(),
        close: vi.fn(),
        readyState: 1, // OPEN
        addEventListener: vi.fn(),
        removeEventListener: vi.fn()
      };

      const connectionId = 'ws-connection-123';
      const userId = 'user-456';

      // Step 1: Store Connection
      testContext.redis.set.mockResolvedValueOnce('OK');
      testContext.redis.expire.mockResolvedValueOnce(1);

      await testContext.redis.set(`ws:${connectionId}`, JSON.stringify({
        userId,
        connectedAt: Date.now(),
        status: 'active'
      }));

      await testContext.redis.expire(`ws:${connectionId}`, 7200); // 2 hours

      // Step 2: Send Message
      const message = {
        type: 'notification',
        data: { message: 'Hello, World!', timestamp: Date.now() }
      };

      mockWebSocket.send(JSON.stringify(message));
      expect(mockWebSocket.send).toHaveBeenCalledWith(JSON.stringify(message));

      // Step 3: Store Message History
      const messageKey = `messages:${userId}`;
      testContext.redis.lpush.mockResolvedValueOnce(1);

      await testContext.redis.lpush(messageKey, JSON.stringify(message));
      expect(testContext.redis.lpush).toHaveBeenCalledWith(messageKey, JSON.stringify(message));

      // Step 4: Cleanup Connection
      testContext.redis.del.mockResolvedValueOnce(1);
      
      await testContext.redis.del(`ws:${connectionId}`);
      mockWebSocket.close();

      expect(testContext.redis.del).toHaveBeenCalledWith(`ws:${connectionId}`);
      expect(mockWebSocket.close).toHaveBeenCalled();
    });
  });

  describe('File Upload and Processing Workflow', () => {
    it('should handle complete file upload and processing pipeline', async () => {
      const fileData = {
        filename: 'test-document.pdf',
        size: 1024 * 1024, // 1MB
        mimetype: 'application/pdf',
        buffer: Buffer.from('fake-pdf-content')
      };

      // Step 1: Validate File
      const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png'];
      const maxSize = 5 * 1024 * 1024; // 5MB

      expect(allowedTypes.includes(fileData.mimetype)).toBe(true);
      expect(fileData.size).toBeLessThan(maxSize);

      // Step 2: Upload to Storage
      testContext.supabase.storage.from.mockReturnValue({
        upload: vi.fn().mockResolvedValue({
          data: { path: `uploads/${fileData.filename}` },
          error: null
        })
      });

      const uploadResult = await testContext.supabase.storage
        .from('documents')
        .upload(`uploads/${fileData.filename}`, fileData.buffer);

      expect(uploadResult.data.path).toBe(`uploads/${fileData.filename}`);
      expect(uploadResult.error).toBeNull();

      // Step 3: Store File Metadata
      const fileMetadata = {
        id: 'file-123',
        filename: fileData.filename,
        size: fileData.size,
        mimetype: fileData.mimetype,
        path: uploadResult.data.path,
        uploadedAt: new Date().toISOString(),
        userId: 'user-123'
      };

      testContext.supabase.from.mockReturnValue({
        insert: vi.fn().mockReturnValue({
          select: vi.fn().mockResolvedValue({
            data: [fileMetadata],
            error: null
          })
        })
      });

      const metadataResult = await testContext.supabase
        .from('files')
        .insert(fileMetadata)
        .select();

      expect(metadataResult.data).toEqual([fileMetadata]);

      // Step 4: Process File (simulate)
      const processingResult = {
        fileId: fileMetadata.id,
        status: 'processed',
        processedAt: new Date().toISOString(),
        metadata: {
          pages: 10,
          wordCount: 1500
        }
      };

      // Store processing results
      testContext.redis.set.mockResolvedValueOnce('OK');
      await testContext.redis.set(
        `file_processing:${fileMetadata.id}`,
        JSON.stringify(processingResult)
      );

      expect(testContext.redis.set).toHaveBeenCalledWith(
        `file_processing:${fileMetadata.id}`,
        JSON.stringify(processingResult)
      );
    });
  });

  describe('Error Handling and Recovery', () => {
    it('should handle database connection failures gracefully', async () => {
      // Simulate database connection failure
      testContext.supabase.from.mockReturnValue({
        select: vi.fn().mockRejectedValue(new Error('Database connection failed'))
      });

      let errorCaught = false;
      let fallbackUsed = false;

      try {
        await testContext.supabase.from('items').select();
      } catch (error) {
        errorCaught = true;
        
        // Fallback to cache
        testContext.redis.get.mockResolvedValueOnce(JSON.stringify([
          { id: 'cached-item', name: 'Cached Item' }
        ]));

        const cachedData = await testContext.redis.get('items:cache');
        if (cachedData) {
          fallbackUsed = true;
        }
      }

      expect(errorCaught).toBe(true);
      expect(fallbackUsed).toBe(true);
    });

    it('should handle Redis connection failures gracefully', async () => {
      // Simulate Redis connection failure
      testContext.redis.get.mockRejectedValue(new Error('Redis connection failed'));

      let errorCaught = false;
      let databaseFallbackUsed = false;

      try {
        await testContext.redis.get('cache:key');
      } catch (error) {
        errorCaught = true;
        
        // Fallback to database
        testContext.supabase.from.mockReturnValue({
          select: vi.fn().mockResolvedValue({
            data: [{ id: 'db-item', name: 'Database Item' }],
            error: null
          })
        });

        const dbResult = await testContext.supabase.from('items').select();
        if (dbResult.data) {
          databaseFallbackUsed = true;
        }
      }

      expect(errorCaught).toBe(true);
      expect(databaseFallbackUsed).toBe(true);
    });
  });

  describe('Performance Under Load', () => {
    it('should maintain performance with concurrent operations', async () => {
      const concurrentOperations = 20;
      const startTime = performance.now();

      const operations = Array.from({ length: concurrentOperations }, async (_, index) => {
        // Simulate various operations
        const operations = [
          // Database operation
          async () => {
            testContext.supabase.from.mockReturnValue({
              select: vi.fn().mockResolvedValue({
                data: [{ id: `item-${index}` }],
                error: null
              })
            });
            return testContext.supabase.from('items').select();
          },
          
          // Cache operation
          async () => {
            testContext.redis.get.mockResolvedValueOnce(`cached-value-${index}`);
            return testContext.redis.get(`key-${index}`);
          },
          
          // Processing operation
          async () => {
            await new Promise(resolve => setTimeout(resolve, 10));
            return { processed: true, index };
          }
        ];

        const randomOperation = operations[index % operations.length];
        return randomOperation();
      });

      const results = await Promise.all(operations);
      const endTime = performance.now();
      const duration = endTime - startTime;

      expect(results).toHaveLength(concurrentOperations);
      expect(duration).toBeLessThan(1000); // Should complete within 1 second
    });
  });
});
