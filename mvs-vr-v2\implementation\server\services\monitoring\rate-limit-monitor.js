/**
 * Rate Limit Monitoring Service
 * 
 * Monitors and tracks rate limiting events across the application
 */

const EventEmitter = require('events');
const { logger } = require('../../utils/logger');

// Rate limit event storage
const rateLimitEvents = new Map();
const ipMetrics = new Map();

/**
 * Track a rate limit event
 * @param {Object} event - Rate limit event data
 * @param {string} event.ip - IP address
 * @param {string} event.endpoint - API endpoint
 * @param {string} event.method - HTTP method
 * @param {number} event.timestamp - Event timestamp
 * @param {string} event.reason - Rate limit reason
 */
function trackRateLimitEvent(event) {
  const eventId = `${event.ip}-${Date.now()}`;
  
  // Store the event
  rateLimitEvents.set(eventId, {
    ...event,
    timestamp: event.timestamp || Date.now()
  });
  
  // Update IP metrics
  const ipKey = event.ip;
  if (!ipMetrics.has(ipKey)) {
    ipMetrics.set(ipKey, {
      ip: event.ip,
      totalEvents: 0,
      endpoints: new Set(),
      firstSeen: event.timestamp || Date.now(),
      lastSeen: event.timestamp || Date.now()
    });
  }
  
  const ipData = ipMetrics.get(ipKey);
  ipData.totalEvents++;
  ipData.endpoints.add(event.endpoint);
  ipData.lastSeen = event.timestamp || Date.now();
  
  logger.debug('Rate limit event tracked', { event });
}

/**
 * Get rate limit metrics
 * @returns {Object} Metrics data
 */
function getMetrics() {
  const now = Date.now();
  const oneHour = 60 * 60 * 1000;
  const oneDay = 24 * oneHour;
  
  // Filter recent events
  const recentEvents = Array.from(rateLimitEvents.values())
    .filter(event => (now - event.timestamp) < oneDay);
  
  const hourlyEvents = recentEvents
    .filter(event => (now - event.timestamp) < oneHour);
  
  // Calculate metrics
  const totalEvents = recentEvents.length;
  const hourlyCount = hourlyEvents.length;
  
  // Top endpoints
  const endpointCounts = {};
  recentEvents.forEach(event => {
    endpointCounts[event.endpoint] = (endpointCounts[event.endpoint] || 0) + 1;
  });
  
  const topEndpoints = Object.entries(endpointCounts)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10)
    .map(([endpoint, count]) => ({ endpoint, count }));
  
  // Top IPs
  const topIPs = Array.from(ipMetrics.values())
    .sort((a, b) => b.totalEvents - a.totalEvents)
    .slice(0, 10)
    .map(ip => ({
      ip: ip.ip,
      events: ip.totalEvents,
      endpoints: ip.endpoints.size,
      lastSeen: ip.lastSeen
    }));
  
  return {
    totalEvents,
    hourlyCount,
    topEndpoints,
    topIPs,
    timestamp: now
  };
}

/**
 * Get top rate limited IPs
 * @param {number} limit - Number of IPs to return
 * @returns {Array} Top rate limited IPs
 */
function getTopRateLimitedIPs(limit = 10) {
  return Array.from(ipMetrics.values())
    .sort((a, b) => b.totalEvents - a.totalEvents)
    .slice(0, limit)
    .map(ip => ({
      ip: ip.ip,
      events: ip.totalEvents,
      endpoints: Array.from(ip.endpoints),
      firstSeen: ip.firstSeen,
      lastSeen: ip.lastSeen
    }));
}

/**
 * Clear old events (cleanup)
 * @param {number} maxAge - Maximum age in milliseconds
 */
function clearOldEvents(maxAge = 24 * 60 * 60 * 1000) { // 24 hours default
  const now = Date.now();
  const cutoff = now - maxAge;
  
  // Clear old events
  for (const [eventId, event] of rateLimitEvents.entries()) {
    if (event.timestamp < cutoff) {
      rateLimitEvents.delete(eventId);
    }
  }
  
  // Clear old IP metrics
  for (const [ip, data] of ipMetrics.entries()) {
    if (data.lastSeen < cutoff) {
      ipMetrics.delete(ip);
    }
  }
  
  logger.debug('Cleared old rate limit events', { cutoff, remaining: rateLimitEvents.size });
}

/**
 * Rate Limit Monitor Class
 */
class RateLimitMonitor extends EventEmitter {
  constructor(options = {}) {
    super();
    this.options = {
      cleanupInterval: 60 * 60 * 1000, // 1 hour
      maxEventAge: 24 * 60 * 60 * 1000, // 24 hours
      ...options
    };
    
    // Start cleanup timer
    this.cleanupTimer = setInterval(() => {
      clearOldEvents(this.options.maxEventAge);
    }, this.options.cleanupInterval);
  }
  
  track(event) {
    trackRateLimitEvent(event);
    this.emit('rateLimitEvent', event);
  }
  
  getMetrics() {
    return getMetrics();
  }
  
  getTopIPs(limit) {
    return getTopRateLimitedIPs(limit);
  }
  
  destroy() {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
  }
}

// Export functions and data for testing
module.exports = {
  trackRateLimitEvent,
  getMetrics,
  getTopRateLimitedIPs,
  clearOldEvents,
  rateLimitEvents,
  ipMetrics,
  RateLimitMonitor
};
