/**
 * Performance Monitor Service
 * Comprehensive performance monitoring with real-time metrics, alerting, and optimization
 */

import { EventEmitter } from 'events';
import { performance, PerformanceObserver } from 'perf_hooks';
import os from 'os';
import v8 from 'v8';

export class PerformanceMonitor extends EventEmitter {
  constructor(options = {}) {
    super();

    this.options = {
      enableRealTimeMonitoring: options.enableRealTimeMonitoring !== false,
      enableResourceMonitoring: options.enableResourceMonitoring !== false,
      enableApplicationMonitoring: options.enableApplicationMonitoring !== false,
      enableNetworkMonitoring: options.enableNetworkMonitoring !== false,
      monitoringInterval: options.monitoringInterval || 5000, // 5 seconds
      alertThresholds: {
        cpuUsage: options.alertThresholds?.cpuUsage || 80, // 80%
        memoryUsage: options.alertThresholds?.memoryUsage || 85, // 85%
        responseTime: options.alertThresholds?.responseTime || 1000, // 1 second
        errorRate: options.alertThresholds?.errorRate || 5, // 5%
        diskUsage: options.alertThresholds?.diskUsage || 90, // 90%
        ...options.alertThresholds,
      },
      retentionPeriod: options.retentionPeriod || 3600000, // 1 hour
      enablePredictiveAnalysis: options.enablePredictiveAnalysis || true,
      enableAutoOptimization: options.enableAutoOptimization || false,
      ...options,
    };

    // Performance data storage
    this.metrics = {
      system: new Map(),
      application: new Map(),
      network: new Map(),
      custom: new Map(),
    };

    // Performance observers
    this.observers = new Map();

    // Alert management
    this.activeAlerts = new Map();
    this.alertHistory = [];

    // Performance baselines
    this.baselines = new Map();

    // Optimization recommendations
    this.recommendations = [];

    // Monitoring timers
    this.monitoringTimers = new Map();

    this.initialize();
  }

  async initialize() {
    try {
      console.log('📊 Initializing Performance Monitor...');

      // Setup performance observers
      this.setupPerformanceObservers();

      // Start monitoring
      if (this.options.enableRealTimeMonitoring) {
        this.startRealTimeMonitoring();
      }

      if (this.options.enableResourceMonitoring) {
        this.startResourceMonitoring();
      }

      if (this.options.enableApplicationMonitoring) {
        this.startApplicationMonitoring();
      }

      if (this.options.enableNetworkMonitoring) {
        this.startNetworkMonitoring();
      }

      // Setup baseline collection
      this.startBaselineCollection();

      // Setup cleanup
      this.startCleanup();

      console.log('✅ Performance Monitor initialized');
      this.emit('ready');
    } catch (error) {
      console.error('❌ Failed to initialize Performance Monitor:', error);
      this.emit('error', error);
    }
  }

  setupPerformanceObservers() {
    // HTTP performance observer
    const httpObserver = new PerformanceObserver(list => {
      for (const entry of list.getEntries()) {
        this.recordHttpPerformance(entry);
      }
    });
    httpObserver.observe({ entryTypes: ['measure'] });
    this.observers.set('http', httpObserver);

    // Function performance observer
    const functionObserver = new PerformanceObserver(list => {
      for (const entry of list.getEntries()) {
        this.recordFunctionPerformance(entry);
      }
    });
    functionObserver.observe({ entryTypes: ['function'] });
    this.observers.set('function', functionObserver);

    // GC performance observer
    const gcObserver = new PerformanceObserver(list => {
      for (const entry of list.getEntries()) {
        this.recordGCPerformance(entry);
      }
    });
    gcObserver.observe({ entryTypes: ['gc'] });
    this.observers.set('gc', gcObserver);
  }

  startRealTimeMonitoring() {
    const timer = setInterval(() => {
      this.collectRealTimeMetrics();
    }, 1000); // Every second for real-time data

    this.monitoringTimers.set('realtime', timer);
  }

  startResourceMonitoring() {
    const timer = setInterval(() => {
      this.collectResourceMetrics();
    }, this.options.monitoringInterval);

    this.monitoringTimers.set('resource', timer);
  }

  startApplicationMonitoring() {
    const timer = setInterval(() => {
      this.collectApplicationMetrics();
    }, this.options.monitoringInterval);

    this.monitoringTimers.set('application', timer);
  }

  startNetworkMonitoring() {
    const timer = setInterval(() => {
      this.collectNetworkMetrics();
    }, this.options.monitoringInterval * 2); // Less frequent for network

    this.monitoringTimers.set('network', timer);
  }

  startBaselineCollection() {
    // Collect baselines every hour
    const timer = setInterval(() => {
      this.collectBaselines();
    }, 3600000);

    this.monitoringTimers.set('baseline', timer);
  }

  startCleanup() {
    // Cleanup old data every 10 minutes
    const timer = setInterval(() => {
      this.cleanupOldData();
    }, 600000);

    this.monitoringTimers.set('cleanup', timer);
  }

  collectRealTimeMetrics() {
    const timestamp = Date.now();

    // CPU usage (approximation)
    const cpuUsage = process.cpuUsage();
    const cpuPercent = this.calculateCPUPercent(cpuUsage);

    // Memory usage
    const memUsage = process.memoryUsage();
    const memPercent = (memUsage.heapUsed / memUsage.heapTotal) * 100;

    // Event loop lag
    const eventLoopLag = this.measureEventLoopLag();

    const metrics = {
      timestamp,
      cpu: {
        usage: cpuPercent,
        user: cpuUsage.user,
        system: cpuUsage.system,
      },
      memory: {
        usage: memPercent,
        heapUsed: memUsage.heapUsed,
        heapTotal: memUsage.heapTotal,
        rss: memUsage.rss,
        external: memUsage.external,
      },
      eventLoop: {
        lag: eventLoopLag,
      },
    };

    this.metrics.system.set(timestamp, metrics);

    // Check for alerts
    this.checkAlerts(metrics);

    this.emit('realTimeMetrics', metrics);
  }

  collectResourceMetrics() {
    const timestamp = Date.now();

    // System information
    const systemInfo = {
      timestamp,
      cpu: {
        cores: os.cpus().length,
        model: os.cpus()[0]?.model || 'unknown',
        speed: os.cpus()[0]?.speed || 0,
        loadAverage: os.loadavg(),
      },
      memory: {
        total: os.totalmem(),
        free: os.freemem(),
        usage: ((os.totalmem() - os.freemem()) / os.totalmem()) * 100,
      },
      disk: this.getDiskUsage(),
      network: this.getNetworkInterfaces(),
      uptime: os.uptime(),
      platform: os.platform(),
      arch: os.arch(),
    };

    this.metrics.system.set(`resource_${timestamp}`, systemInfo);

    // Check resource alerts
    this.checkResourceAlerts(systemInfo);

    this.emit('resourceMetrics', systemInfo);
  }

  collectApplicationMetrics() {
    const timestamp = Date.now();

    // V8 heap statistics
    const heapStats = v8.getHeapStatistics();
    const heapSpaceStats = v8.getHeapSpaceStatistics();

    // Application metrics
    const appMetrics = {
      timestamp,
      heap: heapStats,
      heapSpaces: heapSpaceStats,
      handles: process._getActiveHandles().length,
      requests: process._getActiveRequests().length,
      uptime: process.uptime(),
      version: process.version,
      pid: process.pid,
      ppid: process.ppid,
    };

    this.metrics.application.set(timestamp, appMetrics);

    this.emit('applicationMetrics', appMetrics);
  }

  collectNetworkMetrics() {
    const timestamp = Date.now();

    // Network statistics (simplified)
    const networkMetrics = {
      timestamp,
      interfaces: this.getNetworkInterfaces(),
      connections: this.getActiveConnections(),
    };

    this.metrics.network.set(timestamp, networkMetrics);

    this.emit('networkMetrics', networkMetrics);
  }

  recordHttpPerformance(entry) {
    const timestamp = Date.now();

    const httpMetric = {
      timestamp,
      name: entry.name,
      duration: entry.duration,
      startTime: entry.startTime,
      entryType: entry.entryType,
    };

    this.metrics.application.set(`http_${timestamp}`, httpMetric);

    // Check for slow requests
    if (entry.duration > this.options.alertThresholds.responseTime) {
      this.triggerAlert('slow_request', {
        duration: entry.duration,
        threshold: this.options.alertThresholds.responseTime,
        request: entry.name,
      });
    }
  }

  recordFunctionPerformance(entry) {
    const timestamp = Date.now();

    const functionMetric = {
      timestamp,
      name: entry.name,
      duration: entry.duration,
      startTime: entry.startTime,
    };

    this.metrics.application.set(`function_${timestamp}`, functionMetric);
  }

  recordGCPerformance(entry) {
    const timestamp = Date.now();

    const gcMetric = {
      timestamp,
      kind: entry.kind,
      duration: entry.duration,
      startTime: entry.startTime,
    };

    this.metrics.application.set(`gc_${timestamp}`, gcMetric);

    // Alert on frequent or long GC
    if (entry.duration > 100) {
      // 100ms
      this.triggerAlert('long_gc', {
        duration: entry.duration,
        kind: entry.kind,
      });
    }
  }

  calculateCPUPercent(cpuUsage) {
    // Simplified CPU percentage calculation
    if (!this.lastCpuUsage) {
      this.lastCpuUsage = cpuUsage;
      return 0;
    }

    const userDiff = cpuUsage.user - this.lastCpuUsage.user;
    const systemDiff = cpuUsage.system - this.lastCpuUsage.system;
    const totalDiff = userDiff + systemDiff;

    this.lastCpuUsage = cpuUsage;

    // Convert microseconds to percentage (approximation)
    return Math.min(100, (totalDiff / 1000000) * 100);
  }

  measureEventLoopLag() {
    // Simplified synchronous event loop lag measurement
    const start = performance.now();
    let lag = 0;

    // Use a simple synchronous approximation
    setImmediate(() => {
      lag = performance.now() - start;
    });

    // Return the last measured lag or 0
    return this.lastEventLoopLag || 0;
  }

  async measureEventLoopLagAsync() {
    const start = performance.now();
    return new Promise(resolve => {
      setImmediate(() => {
        const lag = performance.now() - start;
        this.lastEventLoopLag = lag;
        resolve(lag);
      });
    });
  }

  getDiskUsage() {
    // Simplified disk usage (would need platform-specific implementation)
    try {
      const stats = require('fs').statSync('.');
      return {
        total: stats.size || 0,
        free: 0, // Would need platform-specific calculation
        usage: 0,
      };
    } catch (error) {
      return { total: 0, free: 0, usage: 0 };
    }
  }

  getNetworkInterfaces() {
    const interfaces = os.networkInterfaces();
    const result = {};

    for (const [name, addresses] of Object.entries(interfaces)) {
      result[name] = addresses.map(addr => ({
        address: addr.address,
        family: addr.family,
        internal: addr.internal,
      }));
    }

    return result;
  }

  getActiveConnections() {
    // Simplified active connections count
    return {
      total: process._getActiveHandles().length,
      requests: process._getActiveRequests().length,
    };
  }

  checkAlerts(metrics) {
    // CPU alert
    if (metrics.cpu.usage > this.options.alertThresholds.cpuUsage) {
      this.triggerAlert('high_cpu', {
        current: metrics.cpu.usage,
        threshold: this.options.alertThresholds.cpuUsage,
      });
    }

    // Memory alert
    if (metrics.memory.usage > this.options.alertThresholds.memoryUsage) {
      this.triggerAlert('high_memory', {
        current: metrics.memory.usage,
        threshold: this.options.alertThresholds.memoryUsage,
      });
    }

    // Event loop lag alert
    if (metrics.eventLoop.lag > 100) {
      // 100ms
      this.triggerAlert('event_loop_lag', {
        current: metrics.eventLoop.lag,
        threshold: 100,
      });
    }
  }

  checkResourceAlerts(systemInfo) {
    // Disk usage alert
    if (systemInfo.disk.usage > this.options.alertThresholds.diskUsage) {
      this.triggerAlert('high_disk_usage', {
        current: systemInfo.disk.usage,
        threshold: this.options.alertThresholds.diskUsage,
      });
    }

    // System memory alert
    if (systemInfo.memory.usage > this.options.alertThresholds.memoryUsage) {
      this.triggerAlert('high_system_memory', {
        current: systemInfo.memory.usage,
        threshold: this.options.alertThresholds.memoryUsage,
      });
    }
  }

  triggerAlert(type, data) {
    const alertId = `${type}_${Date.now()}`;
    const alert = {
      id: alertId,
      type,
      data,
      timestamp: Date.now(),
      severity: this.getAlertSeverity(type, data),
      resolved: false,
    };

    this.activeAlerts.set(alertId, alert);
    this.alertHistory.push(alert);

    this.emit('alert', alert);

    // Auto-resolve some alerts after a period
    setTimeout(() => {
      this.resolveAlert(alertId);
    }, 300000); // 5 minutes
  }

  resolveAlert(alertId) {
    const alert = this.activeAlerts.get(alertId);
    if (alert) {
      alert.resolved = true;
      alert.resolvedAt = Date.now();
      this.activeAlerts.delete(alertId);
      this.emit('alertResolved', alert);
    }
  }

  getAlertSeverity(type, data) {
    const severityMap = {
      high_cpu: 'warning',
      high_memory: 'warning',
      high_disk_usage: 'critical',
      slow_request: 'warning',
      long_gc: 'info',
      event_loop_lag: 'warning',
    };

    return severityMap[type] || 'info';
  }

  collectBaselines() {
    const now = Date.now();
    const hourAgo = now - 3600000;

    // Collect baseline metrics for the past hour
    const systemMetrics = Array.from(this.metrics.system.values()).filter(
      m => m.timestamp > hourAgo,
    );

    if (systemMetrics.length > 0) {
      const baseline = {
        timestamp: now,
        cpu: {
          avg: this.average(systemMetrics.map(m => m.cpu?.usage || 0)),
          max: Math.max(...systemMetrics.map(m => m.cpu?.usage || 0)),
          min: Math.min(...systemMetrics.map(m => m.cpu?.usage || 0)),
        },
        memory: {
          avg: this.average(systemMetrics.map(m => m.memory?.usage || 0)),
          max: Math.max(...systemMetrics.map(m => m.memory?.usage || 0)),
          min: Math.min(...systemMetrics.map(m => m.memory?.usage || 0)),
        },
      };

      this.baselines.set(now, baseline);
      this.emit('baselineCollected', baseline);
    }
  }

  cleanupOldData() {
    const cutoff = Date.now() - this.options.retentionPeriod;

    // Clean up old metrics
    for (const [category, metrics] of Object.entries(this.metrics)) {
      for (const [timestamp, data] of metrics) {
        if (typeof timestamp === 'number' && timestamp < cutoff) {
          metrics.delete(timestamp);
        } else if (typeof timestamp === 'string' && data.timestamp < cutoff) {
          metrics.delete(timestamp);
        }
      }
    }

    // Clean up old baselines
    for (const [timestamp, baseline] of this.baselines) {
      if (timestamp < cutoff) {
        this.baselines.delete(timestamp);
      }
    }

    // Clean up old alert history
    this.alertHistory = this.alertHistory.filter(alert => alert.timestamp > cutoff);
  }

  average(numbers) {
    return numbers.length > 0 ? numbers.reduce((a, b) => a + b, 0) / numbers.length : 0;
  }

  /**
   * Get current performance summary
   */
  getPerformanceSummary() {
    const latest = Array.from(this.metrics.system.values())
      .filter(m => typeof m.timestamp === 'number')
      .sort((a, b) => b.timestamp - a.timestamp)[0];

    return {
      current: latest,
      alerts: {
        active: this.activeAlerts.size,
        total: this.alertHistory.length,
      },
      baselines: this.baselines.size,
      uptime: process.uptime(),
      timestamp: Date.now(),
    };
  }

  /**
   * Get performance metrics for a time range
   */
  getMetricsForTimeRange(startTime, endTime) {
    const result = {
      system: [],
      application: [],
      network: [],
    };

    for (const [category, metrics] of Object.entries(this.metrics)) {
      for (const [timestamp, data] of metrics) {
        const time = typeof timestamp === 'number' ? timestamp : data.timestamp;
        if (time >= startTime && time <= endTime) {
          result[category]?.push(data);
        }
      }
    }

    return result;
  }

  getMetrics() {
    return {
      systemMetrics: this.metrics.system.size,
      applicationMetrics: this.metrics.application.size,
      networkMetrics: this.metrics.network.size,
      activeAlerts: this.activeAlerts.size,
      alertHistory: this.alertHistory.length,
      baselines: this.baselines.size,
      observers: this.observers.size,
      timers: this.monitoringTimers.size,
    };
  }

  async shutdown() {
    console.log('🛑 Shutting down Performance Monitor...');

    // Clear all timers
    for (const timer of this.monitoringTimers.values()) {
      clearInterval(timer);
    }

    // Disconnect observers
    for (const observer of this.observers.values()) {
      observer.disconnect();
    }

    // Clear data
    for (const metrics of Object.values(this.metrics)) {
      metrics.clear();
    }

    this.activeAlerts.clear();
    this.baselines.clear();
    this.observers.clear();
    this.monitoringTimers.clear();

    console.log('✅ Performance Monitor shutdown complete');
  }
}

export default PerformanceMonitor;
