/**
 * Enhanced Performance Monitoring Service
 * 
 * Comprehensive performance monitoring with metrics collection,
 * alerting, and automated optimization recommendations
 */

const EventEmitter = require('events');
const { logger } = require('../../utils/logger');

/**
 * Performance metric types
 */
const METRIC_TYPES = {
  RESPONSE_TIME: 'response_time',
  THROUGHPUT: 'throughput',
  ERROR_RATE: 'error_rate',
  CPU_USAGE: 'cpu_usage',
  MEMORY_USAGE: 'memory_usage',
  DATABASE_QUERY_TIME: 'db_query_time',
  CACHE_HIT_RATE: 'cache_hit_rate',
  WEBSOCKET_LATENCY: 'websocket_latency'
};

/**
 * Alert severity levels
 */
const ALERT_LEVELS = {
  INFO: 'info',
  WARNING: 'warning',
  CRITICAL: 'critical'
};

/**
 * Enhanced Performance Monitor
 */
class EnhancedPerformanceMonitor extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      metricsRetentionMs: 24 * 60 * 60 * 1000, // 24 hours
      alertThresholds: {
        [METRIC_TYPES.RESPONSE_TIME]: { warning: 1000, critical: 3000 }, // ms
        [METRIC_TYPES.ERROR_RATE]: { warning: 5, critical: 10 }, // percentage
        [METRIC_TYPES.CPU_USAGE]: { warning: 70, critical: 90 }, // percentage
        [METRIC_TYPES.MEMORY_USAGE]: { warning: 80, critical: 95 }, // percentage
        [METRIC_TYPES.DATABASE_QUERY_TIME]: { warning: 500, critical: 2000 }, // ms
        [METRIC_TYPES.CACHE_HIT_RATE]: { warning: 80, critical: 60 }, // percentage (lower is worse)
        [METRIC_TYPES.WEBSOCKET_LATENCY]: { warning: 200, critical: 500 } // ms
      },
      samplingInterval: 60000, // 1 minute
      ...options
    };
    
    this.metrics = new Map();
    this.alerts = [];
    this.isMonitoring = false;
    this.samplingTimer = null;
    
    // Performance baselines for comparison
    this.baselines = new Map();
    
    // Optimization recommendations
    this.recommendations = [];
  }
  
  /**
   * Start performance monitoring
   */
  start() {
    if (this.isMonitoring) {
      logger.warn('Performance monitoring is already running');
      return;
    }
    
    this.isMonitoring = true;
    
    // Start periodic sampling
    this.samplingTimer = setInterval(() => {
      this.collectSystemMetrics();
    }, this.options.samplingInterval);
    
    logger.info('Enhanced performance monitoring started');
    this.emit('monitoringStarted');
  }
  
  /**
   * Stop performance monitoring
   */
  stop() {
    if (!this.isMonitoring) {
      return;
    }
    
    this.isMonitoring = false;
    
    if (this.samplingTimer) {
      clearInterval(this.samplingTimer);
      this.samplingTimer = null;
    }
    
    logger.info('Enhanced performance monitoring stopped');
    this.emit('monitoringStopped');
  }
  
  /**
   * Record a performance metric
   */
  recordMetric(type, value, metadata = {}) {
    const timestamp = Date.now();
    
    if (!this.metrics.has(type)) {
      this.metrics.set(type, []);
    }
    
    const metric = {
      type,
      value,
      timestamp,
      metadata
    };
    
    this.metrics.get(type).push(metric);
    
    // Clean up old metrics
    this.cleanupOldMetrics(type);
    
    // Check for alerts
    this.checkAlerts(type, value);
    
    // Update baselines
    this.updateBaselines(type, value);
    
    // Generate recommendations
    this.generateRecommendations(type, value);
    
    this.emit('metricRecorded', metric);
    
    logger.debug(`Recorded metric: ${type} = ${value}`, { metadata });
  }
  
  /**
   * Collect system metrics
   */
  collectSystemMetrics() {
    try {
      // CPU usage (mock implementation)
      const cpuUsage = this.getCPUUsage();
      this.recordMetric(METRIC_TYPES.CPU_USAGE, cpuUsage);
      
      // Memory usage
      const memoryUsage = this.getMemoryUsage();
      this.recordMetric(METRIC_TYPES.MEMORY_USAGE, memoryUsage);
      
      // Calculate derived metrics
      this.calculateDerivedMetrics();
      
    } catch (error) {
      logger.error('Error collecting system metrics', { error: error.message });
    }
  }
  
  /**
   * Get CPU usage percentage (mock implementation)
   */
  getCPUUsage() {
    // In a real implementation, this would use system monitoring tools
    return Math.random() * 100;
  }
  
  /**
   * Get memory usage percentage
   */
  getMemoryUsage() {
    const usage = process.memoryUsage();
    const totalMemory = 1024 * 1024 * 1024; // 1GB mock total
    return (usage.heapUsed / totalMemory) * 100;
  }
  
  /**
   * Calculate derived metrics
   */
  calculateDerivedMetrics() {
    // Calculate throughput from response time metrics
    const responseTimeMetrics = this.metrics.get(METRIC_TYPES.RESPONSE_TIME) || [];
    const recentMetrics = responseTimeMetrics.filter(
      m => Date.now() - m.timestamp < 60000 // Last minute
    );
    
    if (recentMetrics.length > 0) {
      const throughput = recentMetrics.length; // Requests per minute
      this.recordMetric(METRIC_TYPES.THROUGHPUT, throughput);
    }
  }
  
  /**
   * Check for alert conditions
   */
  checkAlerts(type, value) {
    const thresholds = this.options.alertThresholds[type];
    if (!thresholds) return;
    
    let alertLevel = null;
    
    if (type === METRIC_TYPES.CACHE_HIT_RATE) {
      // For cache hit rate, lower values are worse
      if (value <= thresholds.critical) {
        alertLevel = ALERT_LEVELS.CRITICAL;
      } else if (value <= thresholds.warning) {
        alertLevel = ALERT_LEVELS.WARNING;
      }
    } else {
      // For other metrics, higher values are worse
      if (value >= thresholds.critical) {
        alertLevel = ALERT_LEVELS.CRITICAL;
      } else if (value >= thresholds.warning) {
        alertLevel = ALERT_LEVELS.WARNING;
      }
    }
    
    if (alertLevel) {
      this.createAlert(type, value, alertLevel);
    }
  }
  
  /**
   * Create an alert
   */
  createAlert(metricType, value, level) {
    const alert = {
      id: `alert-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      metricType,
      value,
      level,
      timestamp: Date.now(),
      message: `${metricType} ${level}: ${value}`,
      acknowledged: false
    };
    
    this.alerts.push(alert);
    
    // Keep only recent alerts
    this.alerts = this.alerts.filter(
      a => Date.now() - a.timestamp < this.options.metricsRetentionMs
    );
    
    logger.warn(`Performance alert: ${alert.message}`, { alert });
    this.emit('alert', alert);
  }
  
  /**
   * Update performance baselines
   */
  updateBaselines(type, value) {
    if (!this.baselines.has(type)) {
      this.baselines.set(type, {
        min: value,
        max: value,
        avg: value,
        count: 1,
        sum: value
      });
    } else {
      const baseline = this.baselines.get(type);
      baseline.min = Math.min(baseline.min, value);
      baseline.max = Math.max(baseline.max, value);
      baseline.count++;
      baseline.sum += value;
      baseline.avg = baseline.sum / baseline.count;
    }
  }
  
  /**
   * Generate optimization recommendations
   */
  generateRecommendations(type, value) {
    const baseline = this.baselines.get(type);
    if (!baseline || baseline.count < 10) return; // Need enough data
    
    // Check if current value is significantly worse than average
    const threshold = baseline.avg * 1.5; // 50% worse than average
    
    if (value > threshold) {
      const recommendation = {
        id: `rec-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        metricType: type,
        currentValue: value,
        baselineAvg: baseline.avg,
        severity: value > baseline.avg * 2 ? 'high' : 'medium',
        message: this.getRecommendationMessage(type, value, baseline),
        timestamp: Date.now()
      };
      
      // Avoid duplicate recommendations
      const existingRec = this.recommendations.find(
        r => r.metricType === type && Date.now() - r.timestamp < 300000 // 5 minutes
      );
      
      if (!existingRec) {
        this.recommendations.push(recommendation);
        this.emit('recommendation', recommendation);
      }
    }
  }
  
  /**
   * Get recommendation message for metric type
   */
  getRecommendationMessage(type, value, baseline) {
    switch (type) {
      case METRIC_TYPES.RESPONSE_TIME:
        return `Response time (${value}ms) is ${Math.round((value / baseline.avg - 1) * 100)}% higher than average. Consider optimizing database queries or adding caching.`;
      case METRIC_TYPES.CPU_USAGE:
        return `CPU usage (${value.toFixed(1)}%) is high. Consider scaling horizontally or optimizing CPU-intensive operations.`;
      case METRIC_TYPES.MEMORY_USAGE:
        return `Memory usage (${value.toFixed(1)}%) is high. Check for memory leaks or consider increasing available memory.`;
      case METRIC_TYPES.DATABASE_QUERY_TIME:
        return `Database query time (${value}ms) is slow. Consider adding indexes or optimizing queries.`;
      case METRIC_TYPES.CACHE_HIT_RATE:
        return `Cache hit rate (${value.toFixed(1)}%) is low. Review caching strategy and cache invalidation policies.`;
      default:
        return `${type} performance is degraded. Current: ${value}, Average: ${baseline.avg.toFixed(2)}`;
    }
  }
  
  /**
   * Clean up old metrics
   */
  cleanupOldMetrics(type) {
    const metrics = this.metrics.get(type);
    const cutoff = Date.now() - this.options.metricsRetentionMs;
    
    const filteredMetrics = metrics.filter(m => m.timestamp > cutoff);
    this.metrics.set(type, filteredMetrics);
  }
  
  /**
   * Get performance summary
   */
  getPerformanceSummary() {
    const summary = {
      timestamp: Date.now(),
      metrics: {},
      alerts: this.alerts.filter(a => !a.acknowledged),
      recommendations: this.recommendations.slice(-10), // Last 10 recommendations
      baselines: Object.fromEntries(this.baselines)
    };
    
    // Calculate current averages for each metric type
    for (const [type, metrics] of this.metrics.entries()) {
      const recentMetrics = metrics.filter(
        m => Date.now() - m.timestamp < 300000 // Last 5 minutes
      );
      
      if (recentMetrics.length > 0) {
        const values = recentMetrics.map(m => m.value);
        summary.metrics[type] = {
          current: values[values.length - 1],
          avg: values.reduce((a, b) => a + b, 0) / values.length,
          min: Math.min(...values),
          max: Math.max(...values),
          count: values.length
        };
      }
    }
    
    return summary;
  }
  
  /**
   * Acknowledge an alert
   */
  acknowledgeAlert(alertId) {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
      alert.acknowledgedAt = Date.now();
      this.emit('alertAcknowledged', alert);
    }
  }
}

module.exports = {
  EnhancedPerformanceMonitor,
  METRIC_TYPES,
  ALERT_LEVELS
};
