const pino = require('pino');

// Define environment-specific configuration
const isDevelopment = process.env.NODE_ENV === 'development';
const isTest = process.env.NODE_ENV === 'test';

// Configure logger
const pinoConfig = {
  level: process.env.LOG_LEVEL || (isDevelopment ? 'debug' : 'info'),
  
  // Pretty print in development
  transport: isDevelopment
    ? {
        target: 'pino-pretty',
        options: {
          colorize: true,
          translateTime: 'SYS:standard',
          ignore: 'pid,hostname',
        },
      }
    : undefined,
  
  // Disable logging in test environment
  enabled: !isTest,
  
  // Add timestamp
  timestamp: pino.stdTimeFunctions.isoTime,
  
  // Add environment and service name
  base: {
    env: process.env.NODE_ENV,
    service: 'mvs-vr-server',
  },
  
  // Redact sensitive information
  redact: {
    paths: [
      'password',
      'passwordConfirmation',
      'authorization',
      'cookie',
      '*.password',
      '*.token',
      '*.key',
      '*.secret',
      '*.credentials',
    ],
    remove: true,
  },
};

// Create logger instance
const pinoLogger = pino(pinoConfig);

/**
 * Logger utility for consistent logging across the application
 */
const logger = {
  /**
   * Log a trace message
   * @param {string} message - The message to log
   * @param {Record<string, any>} context - Additional context for the log
   */
  trace: (message, context) => {
    pinoLogger.trace(context, message);
  },

  /**
   * Log a debug message
   * @param {string} message - The message to log
   * @param {Record<string, any>} context - Additional context for the log
   */
  debug: (message, context) => {
    pinoLogger.debug(context, message);
  },

  /**
   * Log an info message
   * @param {string} message - The message to log
   * @param {Record<string, any>} context - Additional context for the log
   */
  info: (message, context) => {
    pinoLogger.info(context, message);
  },

  /**
   * Log a warning message
   * @param {string} message - The message to log
   * @param {Record<string, any>} context - Additional context for the log
   */
  warn: (message, context) => {
    pinoLogger.warn(context, message);
  },

  /**
   * Log an error message
   * @param {string} message - The message to log
   * @param {Record<string, any>} context - Additional context for the log
   */
  error: (message, context) => {
    pinoLogger.error(context, message);
  },

  /**
   * Log a fatal message
   * @param {string} message - The message to log
   * @param {Record<string, any>} context - Additional context for the log
   */
  fatal: (message, context) => {
    pinoLogger.fatal(context, message);
  },

  /**
   * Create a child logger with additional context
   * @param {Record<string, any>} bindings - Additional context to include in all logs
   * @returns A new logger instance with the additional context
   */
  child: (bindings) => {
    const childLogger = pinoLogger.child(bindings);
    
    return {
      trace: (message, context) => {
        childLogger.trace(context, message);
      },
      debug: (message, context) => {
        childLogger.debug(context, message);
      },
      info: (message, context) => {
        childLogger.info(context, message);
      },
      warn: (message, context) => {
        childLogger.warn(context, message);
      },
      error: (message, context) => {
        childLogger.error(context, message);
      },
      fatal: (message, context) => {
        childLogger.fatal(context, message);
      },
    };
  },
};

/**
 * Get a logger instance with optional name
 * @param {string} name - Optional logger name for context
 * @returns {Object} Logger instance
 */
function getLogger(name) {
  if (name) {
    return logger.child({ component: name });
  }
  return logger;
}

// Export both the logger and getLogger function
module.exports = {
  logger,
  getLogger,
};
