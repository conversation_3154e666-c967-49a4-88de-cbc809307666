/**
 * Cross-Region Recovery
 *
 * This script implements cross-region recovery testing and procedures.
 */

const { S3Client, ListObjectsV2Command, GetObjectCommand } = require('@aws-sdk/client-s3');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const stream = require('stream');
const { execSync, spawn } = require('child_process');
const { logger } = require('../../shared/utils/logger');
const pipeline = promisify(stream.pipeline);
const writeFileAsync = promisify(fs.writeFile);
const readFileAsync = promisify(fs.readFile);
const mkdirAsync = promisify(fs.mkdir);
const existsAsync = promisify(fs.exists);

// Configuration
const config = {
  primaryRegion: process.env.PRIMARY_REGION || 'us-east-1',
  secondaryRegion: process.env.SECONDARY_REGION || 'us-west-2',
  buckets: {
    database: {
      primary: process.env.PRIMARY_DB_BUCKET || 'mvs-vr-db-backups',
      secondary: process.env.SECONDARY_DB_BUCKET || 'mvs-vr-db-backups-dr',
    },
    files: {
      primary: process.env.PRIMARY_FILES_BUCKET || 'mvs-vr-file-backups',
      secondary: process.env.SECONDARY_FILES_BUCKET || 'mvs-vr-file-backups-dr',
    },
    config: {
      primary: process.env.PRIMARY_CONFIG_BUCKET || 'mvs-vr-config-backups',
      secondary: process.env.SECONDARY_CONFIG_BUCKET || 'mvs-vr-config-backups-dr',
    },
  },
  recoveryLogPath: path.join(__dirname, '../../logs/recovery.json'),
  tempDir: path.join(__dirname, '../../temp/recovery'),
  recoveryScripts: {
    database: path.join(__dirname, '../recovery/database-recovery.js'),
    files: path.join(__dirname, '../recovery/file-recovery.js'),
    config: path.join(__dirname, '../recovery/config-recovery.js'),
  },
  recoveryTimeoutMs: 30 * 60 * 1000, // 30 minutes
  recoveryTestConfig: {
    database: {
      testQueries: [
        'SELECT COUNT(*) FROM users',
        'SELECT COUNT(*) FROM products',
        'SELECT COUNT(*) FROM orders',
      ],
    },
    files: {
      testPaths: ['uploads/products', 'uploads/users', 'uploads/showrooms'],
    },
    config: {
      testKeys: ['system', 'api', 'security'],
    },
  },
};

/**
 * Create S3 client for a specific region
 * @param {string} region - AWS region
 * @returns {S3Client} S3 client
 */
function createS3Client(region) {
  return new S3Client({
    region,
    credentials: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    },
  });
}

/**
 * Load recovery log
 * @returns {Object} Recovery log
 */
async function loadRecoveryLog() {
  try {
    if (await existsAsync(config.recoveryLogPath)) {
      const data = await readFileAsync(config.recoveryLogPath, 'utf8');
      return JSON.parse(data);
    }
  } catch (error) {
    console.error('Error loading recovery log:', error);
  }

  return {
    lastRun: null,
    recoveries: [],
  };
}

/**
 * Save recovery log
 * @param {Object} log - Recovery log
 */
async function saveRecoveryLog(log) {
  try {
    // Ensure directory exists
    const dir = path.dirname(config.recoveryLogPath);
    if (!(await existsAsync(dir))) {
      await mkdirAsync(dir, { recursive: true });
    }

    await writeFileAsync(config.recoveryLogPath, JSON.stringify(log, null, 2), 'utf8');
  } catch (error) {
    console.error('Error saving recovery log:', error);
  }
}

/**
 * Get latest backup from secondary region
 * @param {string} bucketType - Type of bucket (database, files, config)
 * @returns {Promise<Object>} Latest backup object
 */
async function getLatestBackup(bucketType) {
  const client = createS3Client(config.secondaryRegion);
  const bucket = config.buckets[bucketType].secondary;

  const command = new ListObjectsV2Command({
    Bucket: bucket,
    MaxKeys: 10,
  });

  const response = await client.send(command);

  if (!response.Contents || response.Contents.length === 0) {
    throw new Error(`No backups found in ${bucket}`);
  }

  // Sort by last modified (newest first)
  const sortedObjects = response.Contents.sort(
    (a, b) => new Date(b.LastModified) - new Date(a.LastModified),
  );

  return {
    bucket,
    key: sortedObjects[0].Key,
    size: sortedObjects[0].Size,
    lastModified: sortedObjects[0].LastModified,
  };
}

/**
 * Download backup from S3
 * @param {Object} backup - Backup object
 * @param {string} outputPath - Output file path
 * @returns {Promise<void>}
 */
async function downloadBackup(backup, outputPath) {
  const client = createS3Client(config.secondaryRegion);

  const command = new GetObjectCommand({
    Bucket: backup.bucket,
    Key: backup.key,
  });

  const response = await client.send(command);

  // Ensure directory exists
  const dir = path.dirname(outputPath);
  if (!(await existsAsync(dir))) {
    await mkdirAsync(dir, { recursive: true });
  }

  // Write file
  const writeStream = fs.createWriteStream(outputPath);
  await pipeline(response.Body, writeStream);
}

/**
 * Run recovery test for a specific bucket type
 * @param {string} bucketType - Type of bucket (database, files, config)
 * @returns {Promise<Object>} Recovery test results
 */
async function runRecoveryTest(bucketType) {
  logger.info(`Running recovery test for ${bucketType}...`);

  // Ensure temp directory exists
  if (!(await existsAsync(config.tempDir))) {
    await mkdirAsync(config.tempDir, { recursive: true });
  }

  const results = {
    bucketType,
    startTime: new Date().toISOString(),
    endTime: null,
    duration: null,
    success: false,
    details: {},
  };

  try {
    // Get latest backup
    const backup = await getLatestBackup(bucketType);
    logger.info(
      `Found latest backup: ${backup.key} (${(backup.size / 1024 / 1024).toFixed(2)} MB)`,
    );

    // Download backup
    const backupPath = path.join(config.tempDir, path.basename(backup.key));
    await downloadBackup(backup, backupPath);
    logger.info(`Downloaded backup to ${backupPath}`);

    // Run recovery script
    const startTime = Date.now();

    const recoveryScript = config.recoveryScripts[bucketType];
    const recoveryOutput = execSync(
      `node ${recoveryScript} --test --source=${backupPath} --target=${config.tempDir}/${bucketType}`,
      {
        encoding: 'utf8',
        timeout: config.recoveryTimeoutMs,
      },
    );

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000; // Convert to seconds

    // Run validation tests
    const validationResults = await validateRecovery(bucketType);

    results.endTime = new Date().toISOString();
    results.duration = duration;
    results.success = validationResults.success;
    results.details = {
      backup,
      recoveryOutput,
      validation: validationResults,
    };

    logger.info(
      `Recovery test ${results.success ? 'successful' : 'failed'} for ${bucketType} in ${duration} seconds`,
    );
  } catch (error) {
    console.error(`Error testing recovery for ${bucketType}:`, error);

    results.endTime = new Date().toISOString();
    results.duration = 0;
    results.success = false;
    results.details = {
      error: error.message,
    };
  }

  return results;
}

/**
 * Validate recovery for a specific bucket type
 * @param {string} bucketType - Type of bucket (database, files, config)
 * @returns {Promise<Object>} Validation results
 */
async function validateRecovery(bucketType) {
  logger.info(`Validating recovery for ${bucketType}...`);

  const results = {
    success: false,
    tests: [],
  };

  try {
    switch (bucketType) {
      case 'database':
        // For database, we would run test queries
        for (const query of config.recoveryTestConfig.database.testQueries) {
          try {
            // This is a placeholder - in a real implementation, you would connect to the recovered database
            // and run the query
            logger.info(`Running test query: ${query}`);

            // Simulate query execution
            const success = Math.random() > 0.1; // 90% success rate for simulation

            results.tests.push({
              name: query,
              success,
              details: success ? 'Query executed successfully' : 'Query failed',
            });
          } catch (error) {
            results.tests.push({
              name: query,
              success: false,
              details: error.message,
            });
          }
        }
        break;

      case 'files':
        // For files, we would check if key directories exist
        for (const testPath of config.recoveryTestConfig.files.testPaths) {
          try {
            const fullPath = path.join(config.tempDir, bucketType, testPath);
            const exists = await existsAsync(fullPath);

            results.tests.push({
              name: testPath,
              success: exists,
              details: exists ? 'Path exists' : 'Path does not exist',
            });
          } catch (error) {
            results.tests.push({
              name: testPath,
              success: false,
              details: error.message,
            });
          }
        }
        break;

      case 'config':
        // For config, we would check if key configuration files exist
        for (const testKey of config.recoveryTestConfig.config.testKeys) {
          try {
            const configPath = path.join(config.tempDir, bucketType, `${testKey}.json`);
            const exists = await existsAsync(configPath);

            if (exists) {
              // Try to parse the JSON
              const configData = await readFileAsync(configPath, 'utf8');
              JSON.parse(configData); // Will throw if invalid JSON

              results.tests.push({
                name: testKey,
                success: true,
                details: 'Config file exists and is valid JSON',
              });
            } else {
              results.tests.push({
                name: testKey,
                success: false,
                details: 'Config file does not exist',
              });
            }
          } catch (error) {
            results.tests.push({
              name: testKey,
              success: false,
              details: error.message,
            });
          }
        }
        break;
    }

    // Overall success is true if all tests passed
    results.success = results.tests.every(test => test.success);
  } catch (error) {
    console.error(`Error validating recovery for ${bucketType}:`, error);
    results.error = error.message;
  }

  return results;
}

/**
 * Run cross-region recovery tests for all bucket types
 * @returns {Promise<Object>} Recovery test results
 */
async function runAllRecoveryTests() {
  const log = await loadRecoveryLog();
  const startTime = new Date();

  logger.info(`Starting cross-region recovery tests at ${startTime.toISOString()}`);

  const recoveryResults = {
    startTime: startTime.toISOString(),
    endTime: null,
    duration: null,
    buckets: {},
  };

  // Test each bucket type
  for (const bucketType of Object.keys(config.buckets)) {
    try {
      recoveryResults.buckets[bucketType] = await runRecoveryTest(bucketType);
    } catch (error) {
      console.error(`Error testing recovery for ${bucketType}:`, error);
      recoveryResults.buckets[bucketType] = {
        bucketType,
        startTime: new Date().toISOString(),
        endTime: new Date().toISOString(),
        duration: 0,
        success: false,
        details: {
          error: error.message,
        },
      };
    }
  }

  const endTime = new Date();
  const durationMs = endTime - startTime;

  recoveryResults.endTime = endTime.toISOString();
  recoveryResults.duration = durationMs / 1000; // Convert to seconds

  // Update log
  log.lastRun = endTime.toISOString();
  log.recoveries.push(recoveryResults);

  // Keep only the last 100 recoveries
  if (log.recoveries.length > 100) {
    log.recoveries = log.recoveries.slice(-100);
  }

  await saveRecoveryLog(log);

  logger.info(`Completed cross-region recovery tests in ${recoveryResults.duration} seconds`);

  return recoveryResults;
}

// If script is run directly, run recovery tests
if (require.main === module) {
  runAllRecoveryTests()
    .then(results => {
      logger.info('Recovery tests completed:');
      logger.info(JSON.stringify(results, null, 2));
    })
    .catch(error => {
      console.error('Error:', error);
      process.exit(1);
    });
}

module.exports = {
  runAllRecoveryTests,
  runRecoveryTest,
  getLatestBackup,
  downloadBackup,
};
