name: Test Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x, 20.x]
        test-env: [local, staging]
    
    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Setup test environment
      run: |
        cp .env.test.example .env.test
        node scripts/switch-test-env.js ${{ matrix.test-env }}
    
    - name: Run unit tests
      run: npm run test:unit
      env:
        TEST_ENV: ${{ matrix.test-env }}
        CI: true
    
    - name: Run integration tests
      run: npm run test:integration
      env:
        TEST_ENV: ${{ matrix.test-env }}
        CI: true
    
    - name: Run performance tests
      run: npm test tests/unit/performance-testing.test.ts
      env:
        TEST_ENV: ${{ matrix.test-env }}
        CI: true
    
    - name: Run security tests
      run: npm test tests/unit/security-testing.test.ts
      env:
        TEST_ENV: ${{ matrix.test-env }}
        CI: true
    
    - name: Generate coverage report
      run: npm run test:coverage
      env:
        TEST_ENV: ${{ matrix.test-env }}
        CI: true
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false

  performance-benchmark:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run performance benchmarks
      run: npm run test:performance
      env:
        TEST_ENV: local
        CI: true
    
    - name: Store benchmark results
      uses: benchmark-action/github-action-benchmark@v1
      with:
        tool: 'vitest'
        output-file-path: performance-results.json
        github-token: ${{ secrets.GITHUB_TOKEN }}
        auto-push: true

  security-scan:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run security audit
      run: npm audit --audit-level moderate
    
    - name: Run security tests
      run: npm run test:security
      env:
        TEST_ENV: local
        CI: true
    
    - name: Run SAST scan
      uses: github/super-linter@v4
      env:
        DEFAULT_BRANCH: main
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        VALIDATE_TYPESCRIPT_ES: true
        VALIDATE_JAVASCRIPT_ES: true

  deploy-staging:
    runs-on: ubuntu-latest
    needs: [test, performance-benchmark, security-scan]
    if: github.ref == 'refs/heads/develop'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # Add actual deployment commands here
    
    - name: Run staging tests
      run: |
        echo "Running tests against staging..."
        npm run test:staging
      env:
        TEST_ENV: staging
        CI: true

  deploy-production:
    runs-on: ubuntu-latest
    needs: [test, performance-benchmark, security-scan]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Deploy to production
      run: |
        echo "Deploying to production environment..."
        # Add actual deployment commands here
    
    - name: Run smoke tests
      run: |
        echo "Running smoke tests against production..."
        npm run test:smoke
      env:
        TEST_ENV: production
        CI: true
