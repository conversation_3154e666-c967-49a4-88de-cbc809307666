/**
 * Logger Utility
 *
 * This module provides a centralized logging utility for the application.
 */

import winston from 'winston';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

// Get __dirname equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Ensure logs directory exists
const logsDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  debug: 3,
  trace: 4,
};

// Define log colors
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  debug: 'blue',
  trace: 'gray',
};

// Add colors to winston
winston.addColors(colors);

// Create console format
const consoleFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    info => `${info.timestamp} [${info.level}] [${info.module || 'app'}]: ${info.message}`,
  ),
);

// Create file format
const fileFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.json(),
);

// Create default logger
const defaultLogger = winston.createLogger({
  levels,
  level: process.env.LOG_LEVEL || 'info',
  format: fileFormat,
  defaultMeta: { service: 'mvs-vr-server' },
  transports: [
    // Write all logs to console
    new winston.transports.Console({
      format: consoleFormat,
    }),
    // Write all logs to combined.log
    new winston.transports.File({
      filename: path.join(logsDir, 'combined.log'),
      maxsize: 10485760, // 10MB
      maxFiles: 10,
    }),
    // Write error logs to error.log
    new winston.transports.File({
      filename: path.join(logsDir, 'error.log'),
      level: 'error',
      maxsize: 10485760, // 10MB
      maxFiles: 10,
    }),
  ],
});

// Create a map to store module-specific loggers
const loggers = new Map();

/**
 * Get a logger for a specific module
 * @param {string} module - Module name
 * @returns {object} Logger instance
 */
function getLogger(module) {
  if (!module) {
    return defaultLogger;
  }

  // Check if logger already exists
  if (loggers.has(module)) {
    return loggers.get(module);
  }

  // Create a new logger for the module
  const moduleLogger = winston.createLogger({
    levels,
    level: process.env.LOG_LEVEL || 'info',
    format: fileFormat,
    defaultMeta: { service: 'mvs-vr-server', module },
    transports: [
      // Write all logs to console
      new winston.transports.Console({
        format: consoleFormat,
      }),
      // Write all logs to combined.log
      new winston.transports.File({
        filename: path.join(logsDir, 'combined.log'),
        maxsize: 10485760, // 10MB
        maxFiles: 10,
      }),
      // Write error logs to error.log
      new winston.transports.File({
        filename: path.join(logsDir, 'error.log'),
        level: 'error',
        maxsize: 10485760, // 10MB
        maxFiles: 10,
      }),
      // Write module-specific logs
      new winston.transports.File({
        filename: path.join(logsDir, `${module}.log`),
        maxsize: 10485760, // 10MB
        maxFiles: 5,
      }),
    ],
  });

  // Store the logger
  loggers.set(module, moduleLogger);

  return moduleLogger;
}

// Export for both ES modules and CommonJS
export { getLogger };

// CommonJS compatibility
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { getLogger };
}
