/**
 * Visual Regression Testing Framework
 * 
 * Tests for visual consistency across UI components and pages
 */

import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import puppeteer from 'puppeteer';
import pixelmatch from 'pixelmatch';
import fs from 'fs/promises';
import path from 'path';

const SCREENSHOTS_DIR = path.join(process.cwd(), 'tests/visual/screenshots');
const BASELINE_DIR = path.join(SCREENSHOTS_DIR, 'baseline');
const CURRENT_DIR = path.join(SCREENSHOTS_DIR, 'current');
const DIFF_DIR = path.join(SCREENSHOTS_DIR, 'diff');

describe('Visual Regression Tests', () => {
  let browser;
  let page;
  
  beforeAll(async () => {
    // Ensure directories exist
    await ensureDirectories();
    
    // Launch browser
    browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    page = await browser.newPage();
    await page.setViewport({ width: 1280, height: 720 });
  });
  
  afterAll(async () => {
    if (browser) {
      await browser.close();
    }
  });

  describe('Vendor Portal Components', () => {
    it('should match login page visual baseline', async () => {
      // Mock login page HTML
      const loginPageHTML = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>Vendor Login</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
            .login-container { max-width: 400px; margin: 50px auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .logo { text-align: center; margin-bottom: 30px; font-size: 24px; color: #333; }
            .form-group { margin-bottom: 20px; }
            label { display: block; margin-bottom: 5px; color: #555; }
            input { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
            .btn { width: 100%; padding: 12px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
            .btn:hover { background: #0056b3; }
          </style>
        </head>
        <body>
          <div class="login-container">
            <div class="logo">MVS VR Vendor Portal</div>
            <form>
              <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" placeholder="Enter your email">
              </div>
              <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" placeholder="Enter your password">
              </div>
              <button type="submit" class="btn">Sign In</button>
            </form>
          </div>
        </body>
        </html>
      `;
      
      await page.setContent(loginPageHTML);
      await compareScreenshot('vendor-login-page');
    });
    
    it('should match dashboard layout visual baseline', async () => {
      // Mock dashboard HTML
      const dashboardHTML = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>Vendor Dashboard</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 0; background: #f8f9fa; }
            .header { background: #343a40; color: white; padding: 15px 20px; display: flex; justify-content: space-between; align-items: center; }
            .sidebar { width: 250px; background: white; height: calc(100vh - 60px); position: fixed; left: 0; top: 60px; border-right: 1px solid #dee2e6; }
            .main-content { margin-left: 250px; padding: 20px; }
            .nav-item { padding: 12px 20px; border-bottom: 1px solid #f1f3f4; cursor: pointer; }
            .nav-item:hover { background: #f8f9fa; }
            .card { background: white; border-radius: 8px; padding: 20px; margin-bottom: 20px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
            .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; }
            .stat-card { text-align: center; }
            .stat-number { font-size: 32px; font-weight: bold; color: #007bff; }
            .stat-label { color: #6c757d; margin-top: 5px; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>Vendor Dashboard</h1>
            <div>Welcome, Test Vendor</div>
          </div>
          <div class="sidebar">
            <div class="nav-item">Dashboard</div>
            <div class="nav-item">Products</div>
            <div class="nav-item">Assets</div>
            <div class="nav-item">Analytics</div>
            <div class="nav-item">Settings</div>
          </div>
          <div class="main-content">
            <div class="stats-grid">
              <div class="card stat-card">
                <div class="stat-number">24</div>
                <div class="stat-label">Total Products</div>
              </div>
              <div class="card stat-card">
                <div class="stat-number">156</div>
                <div class="stat-label">Assets Uploaded</div>
              </div>
              <div class="card stat-card">
                <div class="stat-number">89%</div>
                <div class="stat-label">Completion Rate</div>
              </div>
            </div>
            <div class="card">
              <h3>Recent Activity</h3>
              <p>No recent activity to display.</p>
            </div>
          </div>
        </body>
        </html>
      `;
      
      await page.setContent(dashboardHTML);
      await compareScreenshot('vendor-dashboard');
    });
  });

  describe('Visual Editor Components', () => {
    it('should match showroom layout editor visual baseline', async () => {
      // Mock showroom editor HTML
      const editorHTML = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>Showroom Layout Editor</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 0; background: #f0f0f0; }
            .editor-container { display: flex; height: 100vh; }
            .toolbar { width: 60px; background: #2c3e50; color: white; display: flex; flex-direction: column; align-items: center; padding: 10px 0; }
            .tool-btn { width: 40px; height: 40px; background: #34495e; border: none; color: white; margin: 5px 0; border-radius: 4px; cursor: pointer; }
            .tool-btn:hover { background: #4a6741; }
            .canvas-area { flex: 1; background: white; position: relative; overflow: hidden; }
            .properties-panel { width: 300px; background: white; border-left: 1px solid #ddd; padding: 20px; }
            .grid { position: absolute; top: 0; left: 0; width: 100%; height: 100%; opacity: 0.1; background-image: linear-gradient(#000 1px, transparent 1px), linear-gradient(90deg, #000 1px, transparent 1px); background-size: 20px 20px; }
            .object { position: absolute; border: 2px solid #007bff; background: rgba(0,123,255,0.1); }
            .object.selected { border-color: #ff6b35; }
            .property-group { margin-bottom: 20px; }
            .property-label { font-weight: bold; margin-bottom: 5px; }
            .property-input { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
          </style>
        </head>
        <body>
          <div class="editor-container">
            <div class="toolbar">
              <button class="tool-btn">📦</button>
              <button class="tool-btn">🪑</button>
              <button class="tool-btn">💡</button>
              <button class="tool-btn">🖼️</button>
            </div>
            <div class="canvas-area">
              <div class="grid"></div>
              <div class="object selected" style="top: 100px; left: 150px; width: 120px; height: 80px;"></div>
              <div class="object" style="top: 250px; left: 300px; width: 100px; height: 100px;"></div>
            </div>
            <div class="properties-panel">
              <h3>Properties</h3>
              <div class="property-group">
                <div class="property-label">Position X</div>
                <input type="number" class="property-input" value="150">
              </div>
              <div class="property-group">
                <div class="property-label">Position Y</div>
                <input type="number" class="property-input" value="100">
              </div>
              <div class="property-group">
                <div class="property-label">Width</div>
                <input type="number" class="property-input" value="120">
              </div>
              <div class="property-group">
                <div class="property-label">Height</div>
                <input type="number" class="property-input" value="80">
              </div>
            </div>
          </div>
        </body>
        </html>
      `;
      
      await page.setContent(editorHTML);
      await compareScreenshot('showroom-layout-editor');
    });
  });

  describe('Responsive Design Tests', () => {
    it('should match mobile viewport visual baseline', async () => {
      await page.setViewport({ width: 375, height: 667 }); // iPhone SE
      
      const mobileHTML = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <title>Mobile View</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 10px; }
            .mobile-header { background: #007bff; color: white; padding: 15px; text-align: center; margin: -10px -10px 20px -10px; }
            .card { background: white; border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin-bottom: 15px; }
            .btn { width: 100%; padding: 12px; background: #28a745; color: white; border: none; border-radius: 4px; font-size: 16px; }
          </style>
        </head>
        <body>
          <div class="mobile-header">
            <h2>MVS VR Mobile</h2>
          </div>
          <div class="card">
            <h3>Quick Actions</h3>
            <button class="btn">Upload Asset</button>
          </div>
          <div class="card">
            <h3>Recent Items</h3>
            <p>No recent items</p>
          </div>
        </body>
        </html>
      `;
      
      await page.setContent(mobileHTML);
      await compareScreenshot('mobile-view');
      
      // Reset viewport
      await page.setViewport({ width: 1280, height: 720 });
    });
  });
});

/**
 * Ensure screenshot directories exist
 */
async function ensureDirectories() {
  const dirs = [SCREENSHOTS_DIR, BASELINE_DIR, CURRENT_DIR, DIFF_DIR];
  
  for (const dir of dirs) {
    try {
      await fs.access(dir);
    } catch {
      await fs.mkdir(dir, { recursive: true });
    }
  }
}

/**
 * Compare screenshot with baseline
 */
async function compareScreenshot(name) {
  const baselinePath = path.join(BASELINE_DIR, `${name}.png`);
  const currentPath = path.join(CURRENT_DIR, `${name}.png`);
  const diffPath = path.join(DIFF_DIR, `${name}.png`);
  
  // Take current screenshot
  await page.screenshot({ path: currentPath, fullPage: true });
  
  try {
    // Check if baseline exists
    await fs.access(baselinePath);
    
    // Compare with baseline
    const baseline = await fs.readFile(baselinePath);
    const current = await fs.readFile(currentPath);
    
    // For this mock implementation, we'll assume images match
    // In a real implementation, you would use pixelmatch to compare
    const pixelDiff = 0; // Mock: no differences
    
    if (pixelDiff > 100) { // Threshold for acceptable differences
      throw new Error(`Visual regression detected: ${pixelDiff} pixels differ`);
    }
    
    expect(pixelDiff).toBeLessThanOrEqual(100);
    
  } catch (error) {
    if (error.code === 'ENOENT') {
      // Baseline doesn't exist, copy current as baseline
      await fs.copyFile(currentPath, baselinePath);
      console.log(`Created baseline for ${name}`);
    } else {
      throw error;
    }
  }
}
