import { defineConfig } from 'vitest/config';
import { resolve } from 'path';
import vue from '@vitejs/plugin-vue';
import { createModuleResolver } from './tests/setup/module-resolver.ts';

const customResolver = createModuleResolver();

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '@directus': resolve(__dirname, './directus/extensions'),
      '@shared': resolve(__dirname, './shared'),
      '@services': resolve(__dirname, './services'),
      '@tests': resolve(__dirname, './tests'),
      '@setup': resolve(__dirname, './tests/setup'),
    },
    extensions: ['.ts', '.js', '.vue', '.json'],
  },
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./tests/setup/run.ts'],
    include: ['**/*.{test,spec,vitest}.{js,ts}', '**/tests/**/*.{js,ts}'],
    exclude: ['**/node_modules/**', '**/dist/**', '**/.{idea,git,cache,output,temp}/**'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov', 'text-summary'],
      exclude: [
        '**/node_modules/**',
        '**/tests/**',
        '**/coverage/**',
        '**/*.d.ts',
        '**/*.config.js',
        '**/scripts/**',
        '**/docs/**',
        '**/*.test.{js,ts}',
        '**/*.spec.{js,ts}',
      ],
      thresholds: {
        global: {
          branches: 70,
          functions: 70,
          lines: 70,
          statements: 70,
        },
        'api/**': {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80,
        },
        'services/**': {
          branches: 75,
          functions: 75,
          lines: 75,
          statements: 75,
        },
      },
      include: [
        'api/**/*.{js,ts}',
        'services/**/*.{js,ts}',
        'middleware/**/*.{js,ts}',
        'shared/**/*.{js,ts}',
      ],
    },
    reporters: ['verbose', 'json', 'html'],
    outputFile: {
      json: './coverage/test-results.json',
      html: './coverage/test-results.html',
    },
  },
  customResolver: {
    resolveId: customResolver.resolveId,
  },
  esbuild: {
    target: 'node18',
    format: 'esm',
    platform: 'node',
  },
  optimizeDeps: {
    entries: ['tests/**/*.{test,spec,vitest}.{js,ts}'],
    include: ['@vue/test-utils', '@testing-library/jest-dom'],
  },
});
